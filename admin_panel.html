<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية - إدارة المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --accent: #e74c3c;
            --success: #27ae60;
            --warning: #f39c12;
            --light: #ecf0f1;
            --dark: #2c3e50;
            --gray: #7f8c8d;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        /* صفحة تسجيل الدخول */
        .login-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            padding: 20px;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .login-logo {
            margin-bottom: 30px;
        }

        .login-logo i {
            font-size: 4rem;
            color: var(--secondary);
            margin-bottom: 15px;
        }

        .login-logo h1 {
            color: var(--primary);
            font-size: 1.8rem;
            font-weight: 700;
        }

        .login-form {
            text-align: right;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--dark);
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            transition: var(--transition);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--secondary);
        }

        .form-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray);
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: var(--secondary);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            font-family: 'Cairo', sans-serif;
        }

        .login-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .error-message {
            background: #ffe6e6;
            color: var(--accent);
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #ffcccb;
            display: none;
        }

        /* لوحة التحكم الرئيسية */
        .admin-dashboard {
            display: none;
            min-height: 100vh;
        }

        .admin-header {
            background: white;
            padding: 20px 30px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .admin-header h1 {
            color: var(--primary);
            font-size: 1.8rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .admin-header h1 i {
            color: var(--secondary);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .logout-btn {
            background: var(--accent);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            font-family: 'Cairo', sans-serif;
        }

        .logout-btn:hover {
            background: #c0392b;
        }

        .main-content {
            padding: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.products { background: var(--secondary); }
        .stat-icon.sales { background: var(--success); }
        .stat-icon.orders { background: var(--warning); }
        .stat-icon.users { background: var(--accent); }

        .stat-info h3 {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .stat-info p {
            color: var(--gray);
            font-weight: 600;
        }

        .admin-card {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .card-header {
            background: var(--primary);
            color: white;
            padding: 20px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-body {
            padding: 25px;
        }

        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: var(--secondary);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success);
        }

        .btn-success:hover {
            background: #219a52;
        }

        .btn-danger {
            background: var(--accent);
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-warning {
            background: var(--warning);
        }

        .btn-warning:hover {
            background: #d68910;
        }

        /* نموذج إضافة المنتج */
        .product-form {
            display: grid;
            gap: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--dark);
            font-weight: 600;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--secondary);
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .image-preview {
            width: 150px;
            height: 150px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
            overflow: hidden;
        }

        .image-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-preview.empty {
            color: var(--gray);
            font-size: 0.9rem;
            text-align: center;
        }

        /* معاينة الصور المتعددة */
        .images-preview {
            margin-top: 15px;
            border: 2px dashed #ddd;
            border-radius: 12px;
            padding: 20px;
            min-height: 120px;
            background: #fafafa;
        }

        .upload-placeholder {
            text-align: center;
            color: var(--gray);
        }

        .upload-placeholder i {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: var(--secondary);
        }

        .upload-placeholder p {
            font-size: 1.1rem;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .upload-placeholder small {
            font-size: 0.9rem;
            color: var(--gray);
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .image-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .image-item img {
            width: 100%;
            height: 100px;
            object-fit: cover;
            display: block;
        }

        .image-item .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: var(--accent);
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .image-item .remove-btn:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        .images-preview.has-images {
            border-color: var(--secondary);
            background: #f8f9ff;
        }

        /* جدول المنتجات */
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .products-table th,
        .products-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #eee;
        }

        .products-table th {
            background: var(--light);
            font-weight: 700;
            color: var(--primary);
            position: sticky;
            top: 0;
        }

        .products-table tr:hover {
            background: #f9f9f9;
        }

        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
        }

        .product-name {
            font-weight: 600;
            color: var(--primary);
        }

        .product-price {
            font-weight: 700;
            color: var(--success);
            font-size: 1.1rem;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .edit-btn {
            background: var(--warning);
            color: white;
        }

        .delete-btn {
            background: var(--accent);
            color: white;
        }

        .view-btn {
            background: var(--secondary);
            color: white;
        }

        /* رسائل التنبيه */
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
            align-items: center;
            gap: 10px;
        }

        .alert.success {
            background: #d4f6d4;
            color: var(--success);
            border: 1px solid #c3e6c3;
        }

        .alert.error {
            background: #ffe6e6;
            color: var(--accent);
            border: 1px solid #ffcccb;
        }

        .alert.show {
            display: flex;
        }

        /* نافذة منبثقة للتأكيد */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }

        .modal-content h3 {
            color: var(--primary);
            margin-bottom: 15px;
        }

        .modal-content p {
            color: var(--gray);
            margin-bottom: 25px;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .admin-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .main-content {
                padding: 15px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .products-table {
                font-size: 0.9rem;
            }

            .products-table th,
            .products-table td {
                padding: 10px 8px;
            }

            .action-buttons {
                flex-direction: column;
            }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: var(--gray);
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- صفحة تسجيل الدخول -->
    <div id="loginPage" class="login-page">
        <div class="login-container">
            <div class="login-logo">
                <i class="fas fa-shield-alt"></i>
                <h1>لوحة التحكم الإدارية</h1>
                <p>إدارة المنتجات والمتجر الإلكتروني</p>
            </div>
            
            <div id="errorMessage" class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <span>اسم المستخدم أو كلمة المرور غير صحيحة</span>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" name="username" required>
                    <i class="fas fa-user"></i>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required>
                    <i class="fas fa-lock"></i>
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    دخول
                </button>
            </form>
            
            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 0.9rem; color: var(--gray);">
                <strong>بيانات الدخول التجريبية:</strong><br>
                اسم المستخدم: admin<br>
                كلمة المرور: admin123
            </div>
        </div>
    </div>

    <!-- لوحة التحكم الرئيسية -->
    <div id="adminDashboard" class="admin-dashboard">
        <!-- رأس الصفحة -->
        <header class="admin-header">
            <h1>
                <i class="fas fa-tachometer-alt"></i>
                لوحة التحكم الإدارية
            </h1>
            <div class="user-info">
                <div class="user-avatar">A</div>
                <span>مرحباً، المدير</span>
                <button id="logoutBtn" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </header>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- إحصائيات سريعة -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon products">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalProducts">0</h3>
                        <p>إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon sales">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <h3>1,250</h3>
                        <p>إجمالي المبيعات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon orders">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-info">
                        <h3>84</h3>
                        <p>الطلبات الجديدة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3>342</h3>
                        <p>العملاء المسجلون</p>
                    </div>
                </div>
            </div>

            <!-- رسائل التنبيه -->
            <div id="alertContainer"></div>

            <!-- نموذج إضافة منتج جديد -->
            <div class="admin-card">
                <div class="card-header">
                    <h2>
                        <i class="fas fa-plus-circle"></i>
                        إضافة منتج جديد
                    </h2>
                    <button type="button" id="clearFormBtn" class="btn btn-warning">
                        <i class="fas fa-eraser"></i>
                        مسح الحقول
                    </button>
                </div>
                <div class="card-body">
                    <form id="productForm" class="product-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="productName">اسم المنتج *</label>
                                <input type="text" id="productName" name="productName" required>
                            </div>
                            <div class="form-group">
                                <label for="productPrice">السعر (ر.س) *</label>
                                <input type="number" id="productPrice" name="productPrice" min="0" step="0.01" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="productCategory">الفئة *</label>
                                <select id="productCategory" name="productCategory" required>
                                    <option value="">اختر فئة المنتج</option>
                                    <option value="whitening">تبييض الأسنان</option>
                                    <option value="corrector">مصحح الأسنان</option>
                                    <option value="toothbrush">فرش الأسنان</option>
                                    <option value="toothpaste">معجون الأسنان</option>
                                    <option value="mouthwash">غسول الفم</option>
                                    <option value="accessories">ملحقات العناية</option>
                                    <option value="tools">أدوات طبية</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="productImages">صور المنتج *</label>
                                <input type="file" id="productImages" name="productImages" accept="image/*" multiple>
                                <div id="imagesPreview" class="images-preview">
                                    <div class="upload-placeholder">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <p>اختر صور المنتج</p>
                                        <small>يمكنك اختيار صور متعددة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="productDescription">وصف المنتج *</label>
                            <textarea id="productDescription" name="productDescription" placeholder="اكتب وصف مفصلاً للمنتج..." required></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="productStock">الكمية المتوفرة</label>
                                <input type="number" id="productStock" name="productStock" min="0" value="1">
                            </div>
                            <div class="form-group">
                                <label for="productDiscount">نسبة الخصم (%)</label>
                                <input type="number" id="productDiscount" name="productDiscount" min="0" max="100" value="0">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i>
                            حفظ المنتج
                        </button>
                    </form>
                </div>
            </div>

            <!-- قائمة المنتجات -->
            <div class="admin-card">
                <div class="card-header">
                    <h2>
                        <i class="fas fa-list"></i>
                        قائمة المنتجات المضافة
                    </h2>
                    <button type="button" id="refreshBtn" class="btn">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
                <div class="card-body">
                    <div id="loading" class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>جاري تحميل المنتجات...</p>
                    </div>
                    
                    <div id="productsTableContainer">
                        <table class="products-table">
                            <thead>
                                <tr>
                                    <th>الصورة</th>
                                    <th>اسم المنتج</th>
                                    <th>الفئة</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>الخصم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- سيتم إدراج المنتجات هنا ديناميكically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- نافذة التأكيد -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <h3>تأكيد الحذف</h3>
            <p>هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.</p>
            <div class="modal-buttons">
                <button id="confirmDelete" class="btn btn-danger">حذف</button>
                <button id="cancelDelete" class="btn">إلغاء</button>
            </div>
        </div>
    </div>

    <script>
        // بيانات النظام
        const ADMIN_CREDENTIALS = {
            username: 'admin',
            password: 'admin123'
        };

        let products = [];
        let editingProductId = null;
        let deleteProductId = null;
        let selectedImages = [];

        // عناصر DOM
        const loginPage = document.getElementById('loginPage');
        const adminDashboard = document.getElementById('adminDashboard');
        const loginForm = document.getElementById('loginForm');
        const errorMessage = document.getElementById('errorMessage');
        const logoutBtn = document.getElementById('logoutBtn');
        const productForm = document.getElementById('productForm');
        const productsTableBody = document.getElementById('productsTableBody');
        const totalProductsEl = document.getElementById('totalProducts');
        const productImagesInput = document.getElementById('productImages');
        const imagesPreview = document.getElementById('imagesPreview');
        const confirmModal = document.getElementById('confirmModal');
        const alertContainer = document.getElementById('alertContainer');

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            checkLoginStatus();
            setupEventListeners();
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تسجيل الدخول
            loginForm.addEventListener('submit', handleLogin);
            logoutBtn.addEventListener('click', handleLogout);

            // إدارة المنتجات
            productForm.addEventListener('submit', handleProductSubmit);
            productImagesInput.addEventListener('change', handleImagesUpload);

            // أزرار أخرى
            document.getElementById('clearFormBtn').addEventListener('click', clearForm);
            document.getElementById('refreshBtn').addEventListener('click', loadProducts);

            // نافذة التأكيد
            document.getElementById('confirmDelete').addEventListener('click', confirmDelete);
            document.getElementById('cancelDelete').addEventListener('click', closeModal);
        }

        // التحقق من حالة تسجيل الدخول
        function checkLoginStatus() {
            const isLoggedIn = sessionStorage.getItem('adminLoggedIn');
            if (isLoggedIn === 'true') {
                showDashboard();
            } else {
                showLoginPage();
            }
        }

        // معالج تسجيل الدخول
        function handleLogin(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {
                sessionStorage.setItem('adminLoggedIn', 'true');
                showDashboard();
                showAlert('تم تسجيل الدخول بنجاح', 'success');
            } else {
                errorMessage.style.display = 'block';
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 3000);
            }
        }

        // معالج تسجيل الخروج
        function handleLogout() {
            sessionStorage.removeItem('adminLoggedIn');
            showLoginPage();
            showAlert('تم تسجيل الخروج بنجاح', 'success');
        }

        // عرض صفحة تسجيل الدخول
        function showLoginPage() {
            loginPage.style.display = 'flex';
            adminDashboard.style.display = 'none';
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
        }

        // عرض لوحة التحكم
        function showDashboard() {
            loginPage.style.display = 'none';
            adminDashboard.style.display = 'block';
            updateStats();
        }

        // معالج إرسال نموذج المنتج
        function handleProductSubmit(e) {
            e.preventDefault();

            if (selectedImages.length === 0) {
                showAlert('يرجى اختيار صورة واحدة على الأقل للمنتج', 'error');
                return;
            }

            const formData = new FormData(productForm);
            const productData = {
                id: editingProductId || Date.now().toString(),
                name: formData.get('productName'),
                price: parseFloat(formData.get('productPrice')),
                category: formData.get('productCategory'),
                image: selectedImages.length === 1 ? selectedImages[0] : selectedImages,
                description: formData.get('productDescription'),
                stock: parseInt(formData.get('productStock')),
                discount: parseInt(formData.get('productDiscount')),
                featured: false
            };

            if (editingProductId) {
                updateProduct(productData);
            } else {
                addProduct(productData);
            }
        }

        // إضافة منتج جديد
        function addProduct(productData) {
            products.push(productData);
            saveProducts();
            
            // إضافة إشعار خاص للصفحة الرئيسية
            localStorage.setItem('productUpdated', Date.now());
            
            loadProducts();
            clearForm();
            showAlert('تم إضافة المنتج بنجاح', 'success');
        }

        // تحديث منتج موجود
        function updateProduct(productData) {
            const index = products.findIndex(p => p.id === editingProductId);
            if (index !== -1) {
                products[index] = productData;
                saveProducts();
                
                // إضافة إشعار خاص للصفحة الرئيسية
                localStorage.setItem('productUpdated', Date.now());
                
                loadProducts();
                clearForm();
                editingProductId = null;
                showAlert('تم تحديث المنتج بنجاح', 'success');
            }
        }

        // حذف منتج
        function deleteProduct(productId) {
            deleteProductId = productId;
            confirmModal.style.display = 'flex';
        }

        // تأكيد الحذف
        function confirmDelete() {
            if (deleteProductId) {
                products = products.filter(p => p.id !== deleteProductId);
                saveProducts();
                
                // إضافة إشعار خاص للصفحة الرئيسية
                localStorage.setItem('productUpdated', Date.now());
                
                loadProducts();
                closeModal();
                showAlert('تم حذف المنتج بنجاح', 'success');
                deleteProductId = null;
            }
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            confirmModal.style.display = 'none';
        }

        // تحرير منتج
        function editProduct(productId) {
            const product = products.find(p => p.id === productId);
            if (product) {
                editingProductId = productId;

                // ملء النموذج بالبيانات
                document.getElementById('productName').value = product.name;
                document.getElementById('productPrice').value = product.price;
                document.getElementById('productCategory').value = product.category;
                document.getElementById('productDescription').value = product.description;
                document.getElementById('productStock').value = product.stock || 1;
                document.getElementById('productDiscount').value = product.discount || 0;

                // تحديث الصور
                selectedImages = Array.isArray(product.image) ? product.image : [product.image];
                updateImagesPreview();

                // التمرير إلى النموذج
                document.getElementById('productForm').scrollIntoView({ behavior: 'smooth' });

                showAlert('تم تحميل بيانات المنتج للتعديل', 'success');
            }
        }

        // مسح النموذج
        function clearForm() {
            productForm.reset();
            editingProductId = null;
            selectedImages = [];
            updateImagesPreview();
        }

        // معالج رفع الصور
        function handleImagesUpload(event) {
            const files = Array.from(event.target.files);

            if (files.length === 0) return;

            selectedImages = [];

            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        selectedImages.push(e.target.result);
                        updateImagesPreview();
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        // تحديث معاينة الصور
        function updateImagesPreview() {
            if (selectedImages.length === 0) {
                imagesPreview.innerHTML = `
                    <div class="upload-placeholder">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>اختر صور المنتج</p>
                        <small>يمكنك اختيار صور متعددة</small>
                    </div>
                `;
                imagesPreview.classList.remove('has-images');
            } else {
                imagesPreview.innerHTML = `
                    <div class="images-grid">
                        ${selectedImages.map((image, index) => `
                            <div class="image-item">
                                <img src="${image}" alt="صورة ${index + 1}">
                                <button type="button" class="remove-btn" onclick="removeImage(${index})">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                `;
                imagesPreview.classList.add('has-images');
            }
        }

        // حذف صورة
        function removeImage(index) {
            selectedImages.splice(index, 1);
            updateImagesPreview();
        }

        // تحميل المنتجات
        function loadProducts() {
            const savedProducts = localStorage.getItem('storeProducts');
            products = savedProducts ? JSON.parse(savedProducts) : [];
            displayProducts();
            updateStats();
        }

        // حفظ المنتجات
        function saveProducts() {
            localStorage.setItem('storeProducts', JSON.stringify(products));
        }

        // عرض المنتجات في الجدول
        function displayProducts() {
            const tbody = productsTableBody;
            tbody.innerHTML = '';

            if (products.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 40px; color: var(--gray);">لا توجد منتجات مضافة بعد</td></tr>';
                return;
            }

            products.forEach(product => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <img src="${product.image}" alt="${product.name}" class="product-image"
                             onerror="this.src='https://via.placeholder.com/60x60?text=صورة'">
                    </td>
                    <td class="product-name">${product.name}</td>
                    <td>${getCategoryName(product.category)}</td>
                    <td class="product-price">${product.price} ر.س</td>
                    <td>${product.stock || 1}</td>
                    <td>${product.discount || 0}%</td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn edit-btn" onclick="editProduct('${product.id}')">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                            <button class="action-btn delete-btn" onclick="deleteProduct('${product.id}')">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // الحصول على اسم الفئة
        function getCategoryName(category) {
            const categories = {
                'whitening': 'تبييض الأسنان',
                'corrector': 'مصحح الأسنان',
                'toothbrush': 'فرش الأسنان',
                'toothpaste': 'معجون الأسنان',
                'mouthwash': 'غسول الفم',
                'accessories': 'ملحقات العناية',
                'tools': 'أدوات طبية',
                'other': 'أخرى'
            };
            return categories[category] || category;
        }

        // تحديث الإحصائيات
        function updateStats() {
            totalProductsEl.textContent = products.length;
        }

        // عرض رسائل التنبيه
        function showAlert(message, type = 'success') {
            const alert = document.createElement('div');
            alert.className = `alert ${type} show`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                <span>${message}</span>
            `;

            alertContainer.appendChild(alert);

            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.addEventListener('click', function(event) {
            if (event.target === confirmModal) {
                closeModal();
            }
        });
    </script>
</body>
</html>


